<template>
  <div class="mainContainer">
    <div id="cesium-viewer" ref="viewerContainer"></div>
    <!-- 视角切换控制面板 -->
    <div class="camera-controls">
      <button
        @click="toggleCameraMode"
        :class="['camera-btn', { active: isFirstPersonView }]"
      >
        {{ isFirstPersonView ? "第一视角" : "第三视角" }}
      </button>
      <button @click="resetAnimation" class="reset-btn">重置动画</button>
      <!-- 新增暂停/继续按钮 -->
      <button @click="togglePause" class="pause-btn">
        {{ isPaused ? "继续" : "暂停" }}
      </button>
      <div v-if="isFirstPersonView" class="first-person-tips">
        <small>第一视角模式：</small>
        <small>• 尾翼视角观察</small>
        <small>• 视角平行于地面</small>
        <small>• 跟随飞机航向</small>
        <small>• 鼠标拖拽旋转视角</small>
      </div>
    </div>
    <!-- 新增：相机视角信息显示 -->
    <div class="camera-info-panel">
      <div>
        <b>相机视角信息</b>
      </div>
      <div>经度: {{ cameraInfo.lon.toFixed(6) }}°</div>
      <div>纬度: {{ cameraInfo.lat.toFixed(6) }}°</div>
      <div>高度: {{ cameraInfo.height.toFixed(2) }} m</div>
      <div>Heading: {{ cameraInfo.heading.toFixed(2) }}°</div>
      <div>Pitch: {{ cameraInfo.pitch.toFixed(2) }}°</div>
      <div>Roll: {{ cameraInfo.roll.toFixed(2) }}°</div>
    </div>

    <!-- 新增：HPR 控制面板 -->
    <div class="hpr-control-panel">
      <div class="panel-header">
        <b>飞行视角控制 (HPR)</b>
        <button @click="toggleHPRPanel" class="toggle-btn">
          {{ showHPRPanel ? "收起" : "展开" }}
        </button>
      </div>
      <div v-show="showHPRPanel" class="hpr-controls">
        <div class="hpr-input-group">
          <label>Heading (航向角):</label>
          <div class="input-with-slider">
            <input
              type="number"
              v-model.number="customHPR.heading"
              @input="updateCustomHPR"
              min="-180"
              max="180"
              step="1"
              class="hpr-input"
            />
            <input
              type="range"
              v-model.number="customHPR.heading"
              @input="updateCustomHPR"
              min="-180"
              max="180"
              step="1"
              class="hpr-slider"
            />
            <span class="unit">°</span>
          </div>
        </div>
        <div class="hpr-input-group">
          <label>Pitch (俯仰角):</label>
          <div class="input-with-slider">
            <input
              type="number"
              v-model.number="customHPR.pitch"
              @input="updateCustomHPR"
              min="-90"
              max="90"
              step="1"
              class="hpr-input"
            />
            <input
              type="range"
              v-model.number="customHPR.pitch"
              @input="updateCustomHPR"
              min="-90"
              max="90"
              step="1"
              class="hpr-slider"
            />
            <span class="unit">°</span>
          </div>
        </div>
        <div class="hpr-input-group">
          <label>Roll (翻滚角):</label>
          <div class="input-with-slider">
            <input
              type="number"
              v-model.number="customHPR.roll"
              @input="updateCustomHPR"
              min="-180"
              max="180"
              step="1"
              class="hpr-input"
            />
            <input
              type="range"
              v-model.number="customHPR.roll"
              @input="updateCustomHPR"
              min="-180"
              max="180"
              step="1"
              class="hpr-slider"
            />
            <span class="unit">°</span>
          </div>
        </div>
        <div class="hpr-buttons">
          <button @click="resetHPR" class="reset-hpr-btn">重置 HPR</button>
          <button @click="syncCurrentHPR" class="sync-btn">同步当前视角</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as Cesium from "cesium";

import { onBeforeUnmount, onMounted, reactive, ref } from "vue";

import { createModel } from "@/utils/createModel";
import { initMap } from "@/utils/initMap";

const viewerContainer = ref<HTMLElement | null>(null);
let viewer: Cesium.Viewer | null = null;
const planURL = "/src/assets/models/plan1.glb";

let aircraftEntity: Cesium.Entity | null = null;
let animationInterval: number | null = null;

// 视角控制相关变量
const isFirstPersonView = ref(false);
let cameraUpdateListener: Cesium.Event.RemoveCallback | null = null;

const cameraInfo = reactive({
  lon: 0,
  lat: 0,
  height: 0,
  heading: 0,
  pitch: 0,
  roll: 0,
});

// HPR 控制相关变量
const showHPRPanel = ref(false);
const customHPR = reactive({
  heading: 0,
  pitch: 0,
  roll: 0,
});

let cameraChangedRemoveCallback: (() => void) | null = null;

// 在北京天安门上空创建飞机模型，设置朝向
// createModel(
//   viewer,
//   planURL,
//   2000, // 高度2000米
//   116.4074, // 北京经度
//   39.9042, // 北京纬度
//   225, // 朝向角度，使飞机朝向东南方
//   10, // 50, // 机头略微向上
//   0, // 0, // 不倾斜
// );

const initViewer = async () => {
  viewer = await initMap(viewerContainer);
  viewer.entities.removeAll();

  const startLon = 116.3975; // 天安门
  const startLat = 39.9087;
  const endLon = 116.3269; // 清华大学
  const endLat = 40.0032;

  // 定义飞行关键高度点：起飞 -> 上升 -> 巡航 -> 下降
  const heights = [100, 800, 1500, 2000, 2000, 2000, 2000, 2000];
  const pointCount = heights.length;
  const secondsBetweenPoints = 3;

  const start = Cesium.JulianDate.now();
  const position = new Cesium.SampledPositionProperty();

  // 设置插值算法为 Hermite，多点之间平滑曲线
  position.setInterpolationOptions({
    interpolationDegree: 5,
    interpolationAlgorithm: Cesium.HermitePolynomialApproximation,
  });

  for (let i = 0; i < pointCount; i++) {
    const lon = startLon + ((endLon - startLon) * i) / (pointCount - 1);
    const lat = startLat + ((endLat - startLat) * i) / (pointCount - 1);
    const height = heights[i];

    const time = Cesium.JulianDate.addSeconds(
      start,
      i * secondsBetweenPoints,
      new Cesium.JulianDate(),
    );
    const positionSample = Cesium.Cartesian3.fromDegrees(lon, lat, height);
    position.addSample(time, positionSample);
  }

  const stop = Cesium.JulianDate.addSeconds(
    start,
    (pointCount - 1) * secondsBetweenPoints,
    new Cesium.JulianDate(),
  );

  aircraftEntity = viewer.entities.add({
    availability: new Cesium.TimeIntervalCollection([
      new Cesium.TimeInterval({ start, stop }),
    ]),
    position,
    orientation: new Cesium.VelocityOrientationProperty(position),
    model: {
      uri: planURL,
      minimumPixelSize: 64,
      maximumScale: 200,
      scale: 2,
    },
  });

  viewer.clock.startTime = start.clone();
  viewer.clock.stopTime = stop.clone();
  viewer.clock.currentTime = start.clone();
  viewer.clock.clockRange = Cesium.ClockRange.CLAMPED;
  viewer.clock.multiplier = 1;
  viewer.clock.shouldAnimate = true;

  // 默认使用第三视角（跟踪模式）
  viewer.trackedEntity = aircraftEntity;

  // 设置初始相机视角
  // viewer.camera.setView({
  //   destination: Cesium.Cartesian3.fromDegrees(116.45, 39.9042, 5000),
  //   orientation: {
  //     heading: Cesium.Math.toRadians(90),
  //     pitch: Cesium.Math.toRadians(-45),
  //     roll: 0.0,
  //   },
  // });
};

// 第一视角相机更新函数
const updateFirstPersonCamera = () => {
  if (!aircraftEntity || !viewer) return;

  const position = aircraftEntity.position?.getValue(viewer.clock.currentTime);
  const orientation = aircraftEntity.orientation?.getValue(
    viewer.clock.currentTime,
  );

  if (position && orientation) {
    // 获取飞机的朝向角度
    const hpr = Cesium.HeadingPitchRoll.fromQuaternion(orientation);

    // 计算相机在飞机坐标系中的偏移位置（尾翼位置）
    const localOffset = new Cesium.Cartesian3(0, -15, 3); // 后方15米，上方3米

    // 将本地偏移转换为世界坐标
    const transform = Cesium.Transforms.headingPitchRollToFixedFrame(
      position,
      hpr,
    );
    const worldOffset = Cesium.Matrix4.multiplyByPoint(
      transform,
      localOffset,
      new Cesium.Cartesian3(),
    );

    // 设置相机朝向：与飞机朝向一致，但俯仰角设为0（平行于地面）
    const cameraHeading = hpr.heading; // 保持飞机的航向
    const cameraPitch = 0; // 平行于地面
    const cameraRoll = 0; // 不倾斜

    // 设置相机位置和朝向
    viewer.scene.camera.setView({
      destination: worldOffset,
      orientation: {
        heading: cameraHeading,
        pitch: cameraPitch,
        roll: cameraRoll,
      },
    });
  }
};

// 切换视角模式
const toggleCameraMode = () => {
  if (!viewer || !aircraftEntity) return;

  isFirstPersonView.value = !isFirstPersonView.value;

  if (isFirstPersonView.value) {
    // 切换到第一视角
    viewer.trackedEntity = undefined;

    // 启用相机控制器，允许用户交互
    viewer.scene.screenSpaceCameraController.enableRotate = true;
    viewer.scene.screenSpaceCameraController.enableZoom = true;
    viewer.scene.screenSpaceCameraController.enableTilt = true;
    viewer.scene.screenSpaceCameraController.enableLook = true;

    // 移除之前的监听器
    if (cameraUpdateListener) {
      cameraUpdateListener();
      cameraUpdateListener = null;
    }

    // 立即设置第一视角位置
    updateFirstPersonCamera();

    // 添加第一视角相机更新监听器（仅更新位置，不锁定朝向）
    cameraUpdateListener = viewer.clock.onTick.addEventListener(() => {
      if (!aircraftEntity || !viewer) return;

      const position = aircraftEntity.position?.getValue(
        viewer.clock.currentTime,
      );
      const orientation = aircraftEntity.orientation?.getValue(
        viewer.clock.currentTime,
      );

      if (position && orientation) {
        // 获取飞机的朝向角度
        const hpr = Cesium.HeadingPitchRoll.fromQuaternion(orientation);

        // 计算相机在飞机坐标系中的偏移位置（尾翼位置）
        const localOffset = new Cesium.Cartesian3(0, -15, 3); // 后方15米，上方3米

        // 将本地偏移转换为世界坐标
        const transform = Cesium.Transforms.headingPitchRollToFixedFrame(
          position,
          hpr,
        );
        const worldOffset = Cesium.Matrix4.multiplyByPoint(
          transform,
          localOffset,
          new Cesium.Cartesian3(),
        );

        // 更新相机位置，并设置朝向为平行于地面
        viewer.scene.camera.position = worldOffset;

        // 设置相机朝向：与飞机朝向一致，但平行于地面
        viewer.scene.camera.setView({
          destination: worldOffset,
          orientation: {
            heading: hpr.heading, // 保持飞机的航向
            pitch: 0, // 平行于地面
            roll: 0, // 不倾斜
          },
        });
      }
    });
  } else {
    // 切换到第三视角
    if (cameraUpdateListener) {
      cameraUpdateListener();
      cameraUpdateListener = null;
    }

    // 恢复跟踪模式
    viewer.trackedEntity = aircraftEntity;

    // 恢复默认相机控制设置
    viewer.scene.screenSpaceCameraController.enableRotate = true;
    viewer.scene.screenSpaceCameraController.enableZoom = true;
    viewer.scene.screenSpaceCameraController.enableTilt = true;
    viewer.scene.screenSpaceCameraController.enableLook = true;
  }
};

// 重置动画
const resetAnimation = () => {
  if (!viewer) return;

  viewer.clock.currentTime = viewer.clock.startTime.clone();
  viewer.clock.shouldAnimate = true;

  // 如果是第一视角，重新设置相机
  if (isFirstPersonView.value) {
    updateFirstPersonCamera();
  }
};

const isPaused = ref(false);

const togglePause = () => {
  if (!viewer) return;
  isPaused.value = !isPaused.value;
  viewer.clock.shouldAnimate = !isPaused.value;
};

// HPR 控制相关函数
const toggleHPRPanel = () => {
  showHPRPanel.value = !showHPRPanel.value;
};

const updateCustomHPR = () => {
  if (!viewer) return;

  // 将角度转换为弧度
  const heading = Cesium.Math.toRadians(customHPR.heading);
  const pitch = Cesium.Math.toRadians(customHPR.pitch);
  const roll = Cesium.Math.toRadians(customHPR.roll);

  // 获取当前相机位置
  const currentPosition = viewer.scene.camera.positionWC;

  // 设置相机视角
  viewer.scene.camera.setView({
    destination: currentPosition,
    orientation: {
      heading: heading,
      pitch: pitch,
      roll: roll,
    },
  });
};

const resetHPR = () => {
  customHPR.heading = 0;
  customHPR.pitch = 0;
  customHPR.roll = 0;
  updateCustomHPR();
};

const syncCurrentHPR = () => {
  if (!viewer) return;

  // 同步当前相机的 HPR 值到控制面板
  customHPR.heading = Cesium.Math.toDegrees(viewer.scene.camera.heading);
  customHPR.pitch = Cesium.Math.toDegrees(viewer.scene.camera.pitch);
  customHPR.roll = Cesium.Math.toDegrees(viewer.scene.camera.roll);
};

onMounted(() => {
  initViewer().then(() => {
    if (viewer) {
      // 初始化相机信息
      const updateCameraInfo = () => {
        const camera = viewer!.scene.camera;
        const carto = Cesium.Cartographic.fromCartesian(camera.positionWC);
        cameraInfo.lon = Cesium.Math.toDegrees(carto.longitude);
        cameraInfo.lat = Cesium.Math.toDegrees(carto.latitude);
        cameraInfo.height = carto.height;
        cameraInfo.heading = Cesium.Math.toDegrees(camera.heading);
        cameraInfo.pitch = Cesium.Math.toDegrees(camera.pitch);
        cameraInfo.roll = Cesium.Math.toDegrees(camera.roll);
      };
      updateCameraInfo();
      // 监听相机变化
      cameraChangedRemoveCallback =
        viewer.scene.camera.changed.addEventListener(updateCameraInfo);
    }
  });
});

onBeforeUnmount(() => {
  if (cameraUpdateListener) {
    cameraUpdateListener();
  }
  if (cameraChangedRemoveCallback) {
    cameraChangedRemoveCallback();
    cameraChangedRemoveCallback = null;
  }
  if (viewer) {
    viewer.destroy();
  }
  if (animationInterval) {
    clearInterval(animationInterval);
  }
});
</script>

<style scoped>
.mainContainer {
  position: relative;
  width: 100%;
  height: 100vh;
}

#cesium-viewer {
  width: 100%;
  height: 100%;
}

.camera-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 1000;
}

.camera-btn,
.reset-btn,
.pause-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  background: rgba(42, 42, 42, 0.8);
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.camera-btn:hover,
.reset-btn:hover,
.pause-btn:hover {
  background: rgba(42, 42, 42, 0.9);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.camera-btn.active {
  background: rgba(0, 122, 255, 0.8);
  border-color: rgba(0, 122, 255, 0.3);
}

.camera-btn.active:hover {
  background: rgba(0, 122, 255, 0.9);
}

.first-person-tips {
  background: rgba(42, 42, 42, 0.9);
  border-radius: 6px;
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.first-person-tips small {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  line-height: 1.4;
  margin: 2px 0;
}

.first-person-tips small:first-child {
  color: rgba(0, 122, 255, 0.9);
  font-weight: 500;
  margin-bottom: 4px;
}

.camera-info-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(42, 42, 42, 0.85);
  color: #fff;
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 13px;
  z-index: 1001;
  min-width: 180px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.18);
  border: 1px solid rgba(255, 255, 255, 0.08);
  user-select: text;
}

.camera-info-panel b {
  color: #00aaff;
  font-size: 14px;
  margin-bottom: 4px;
  display: block;
}

/* HPR 控制面板样式 */
.hpr-control-panel {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(42, 42, 42, 0.9);
  color: #fff;
  border-radius: 8px;
  padding: 16px;
  font-size: 13px;
  z-index: 1002;
  min-width: 300px;
  max-width: 400px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-header b {
  color: #00aaff;
  font-size: 14px;
}

.toggle-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  background: rgba(0, 122, 255, 0.2);
  color: #00aaff;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 122, 255, 0.3);
}

.toggle-btn:hover {
  background: rgba(0, 122, 255, 0.3);
  transform: translateY(-1px);
}

.hpr-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.hpr-input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.hpr-input-group label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  font-weight: 500;
}

.input-with-slider {
  display: flex;
  align-items: center;
  gap: 8px;
}

.hpr-input {
  width: 80px;
  padding: 6px 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  font-size: 12px;
  text-align: center;
}

.hpr-input:focus {
  outline: none;
  border-color: #00aaff;
  box-shadow: 0 0 0 2px rgba(0, 170, 255, 0.2);
}

.hpr-slider {
  flex: 1;
  height: 4px;
  border-radius: 2px;
  background: rgba(255, 255, 255, 0.2);
  outline: none;
  cursor: pointer;
}

.hpr-slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #00aaff;
  cursor: pointer;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hpr-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #00aaff;
  cursor: pointer;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.unit {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  min-width: 15px;
}

.hpr-buttons {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.reset-hpr-btn,
.sync-btn {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reset-hpr-btn {
  background: rgba(255, 59, 48, 0.2);
  color: #ff3b30;
  border: 1px solid rgba(255, 59, 48, 0.3);
}

.reset-hpr-btn:hover {
  background: rgba(255, 59, 48, 0.3);
  transform: translateY(-1px);
}

.sync-btn {
  background: rgba(52, 199, 89, 0.2);
  color: #34c759;
  border: 1px solid rgba(52, 199, 89, 0.3);
}

.sync-btn:hover {
  background: rgba(52, 199, 89, 0.3);
  transform: translateY(-1px);
}
</style>
